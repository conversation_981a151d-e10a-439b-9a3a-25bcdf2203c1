package com.jarvis.appium.cucumber.model;

import java.util.Map;

/**
 * POJO class representing an element locator from generate_locators response
 */
public class ElementLocator {
    
    private String tagName;
    private Map<String, String> locators;
    private String text;
    private String contentDesc;
    private String resourceId;
    private boolean clickable;
    private boolean enabled;
    private boolean displayed;
    private String uuid; // UUID from appium_find_element
    
    // Default constructor
    public ElementLocator() {}
    
    // Constructor with all fields
    public ElementLocator(String tagName, Map<String, String> locators, String text, 
                         String contentDesc, String resourceId, boolean clickable, 
                         boolean enabled, boolean displayed) {
        this.tagName = tagName;
        this.locators = locators;
        this.text = text;
        this.contentDesc = contentDesc;
        this.resourceId = resourceId;
        this.clickable = clickable;
        this.enabled = enabled;
        this.displayed = displayed;
    }
    
    // Getters and Setters
    public String getTagName() {
        return tagName;
    }
    
    public void setTagName(String tagName) {
        this.tagName = tagName;
    }
    
    public Map<String, String> getLocators() {
        return locators;
    }
    
    public void setLocators(Map<String, String> locators) {
        this.locators = locators;
    }
    
    public String getText() {
        return text;
    }
    
    public void setText(String text) {
        this.text = text;
    }
    
    public String getContentDesc() {
        return contentDesc;
    }
    
    public void setContentDesc(String contentDesc) {
        this.contentDesc = contentDesc;
    }
    
    public String getResourceId() {
        return resourceId;
    }
    
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }
    
    public boolean isClickable() {
        return clickable;
    }
    
    public void setClickable(boolean clickable) {
        this.clickable = clickable;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public boolean isDisplayed() {
        return displayed;
    }
    
    public void setDisplayed(boolean displayed) {
        this.displayed = displayed;
    }
    
    public String getUuid() {
        return uuid;
    }
    
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
    
    /**
     * Get the best locator strategy for this element
     * Priority: accessibility id > resource id > xpath
     */
    public String getBestLocatorStrategy() {
        if (locators.containsKey("accessibility id")) {
            return "accessibility id";
        } else if (locators.containsKey("id")) {
            return "id";
        } else if (locators.containsKey("xpath")) {
            return "xpath";
        } else {
            return locators.keySet().iterator().next(); // Return first available
        }
    }
    
    /**
     * Get the locator value for the best strategy
     */
    public String getBestLocatorValue() {
        String strategy = getBestLocatorStrategy();
        return locators.get(strategy);
    }
    
    /**
     * Check if this element can be used for input (text fields)
     */
    public boolean isInputElement() {
        return "android.widget.EditText".equals(tagName) && enabled && displayed;
    }
    
    /**
     * Check if this element can be clicked
     */
    public boolean isClickableElement() {
        return clickable && enabled && displayed;
    }
    
    @Override
    public String toString() {
        return "ElementLocator{" +
                "tagName='" + tagName + '\'' +
                ", text='" + text + '\'' +
                ", contentDesc='" + contentDesc + '\'' +
                ", clickable=" + clickable +
                ", enabled=" + enabled +
                ", displayed=" + displayed +
                ", uuid='" + uuid + '\'' +
                ", bestLocator=" + getBestLocatorStrategy() + ":" + getBestLocatorValue() +
                '}';
    }
}
