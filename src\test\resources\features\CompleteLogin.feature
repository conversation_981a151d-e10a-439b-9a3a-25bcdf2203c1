@CompleteLogin
Feature: Complete Login Test for SauceLabs App
  As a tester
  I want to test the complete login flow with UUID-based element interaction
  So that I can verify all login steps work correctly

  Background:
    Given the SauceLabs app is launched and ready

  @Login @UUID @Smoke
  Scenario: Complete Login with UUID-based Element Interaction
    Given the SauceLabs app is launched
    When I generate locators for current screen
    And I enter username "standard_user"
    And I enter password "secret_sauce"
    And I click the login button
    Then I should be logged in successfully
