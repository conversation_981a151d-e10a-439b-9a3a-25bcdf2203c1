package com.jarvis.appium.examples;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.concurrent.TimeUnit;

/**
 * Run a single Given-When-Then scenario for quick testing
 */
public class SingleScenarioRunnerExtra {

    private static final Logger logger = LoggerFactory.getLogger(SingleScenarioRunnerExtra.class);

    // Configuration - using correct package info
    private static final String APK_PATH = "C:\\POC\\AI\\MCP Integration Server\\mcp-client-integration\\Android.SauceLabs.Mobile.Sample.app.2.7.1.apk";
    private static final String PACKAGE_NAME = "com.swaglabsmobileapp";
    private static final String ACTIVITY_NAME = "com.swaglabsmobileapp.SplashActivity";

    private static BufferedWriter writer;
    private static BufferedReader reader;
    private static BufferedReader errorReader;
    private static int requestId = 1;

    public static void main(String[] args) {
        try {
            logger.info("🎯 Running Single Given-When-Then Scenario");
            logger.info("===========================================");

            // Start MCP server and initialize
            initializeMcp();

            // Run just the first scenario: "View Product Catalog"
            runSingleScenario();

        } catch (Exception e) {
            logger.error("Single scenario execution failed", e);
        }
    }

    private static void initializeMcp() throws IOException, InterruptedException {
        logger.info("🚀 Starting MCP server...");

        // Start MCP server
        String[] command = { "node", "dist/index.js" };
        ProcessBuilder processBuilder = new ProcessBuilder(command);
        processBuilder.directory(new File("C:\\POC\\AI\\MCP Integration Server\\jarvis-appium"));
        processBuilder.redirectErrorStream(false);

        Process process = processBuilder.start();

        writer = new BufferedWriter(new OutputStreamWriter(process.getOutputStream()));
        reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        errorReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));

        // Start output readers
        startOutputReaders();
        Thread.sleep(3000);

        // Initialize MCP
        logger.info("🔗 Initializing MCP connection...");
        sendMcpRequest("initialize",
                "{\"protocolVersion\":\"2024-11-05\",\"clientInfo\":{\"name\":\"single-scenario-test\",\"version\":\"1.0.0\"},\"capabilities\":{}}");
        Thread.sleep(2000);

        // Select Android platform
        logger.info("📱 Selecting Android platform...");
        sendMcpRequest("tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"android\"}}");
        Thread.sleep(2000);

        // Create session
        logger.info("📲 Creating session with SauceLabs app...");
        String sessionCapabilities = "{"
                + "\"platform\":\"android\","
                + "\"capabilities\":{"
                + "\"platformName\":\"Android\","
                + "\"deviceName\":\"emulator-5554\","
                + "\"app\":\"" + APK_PATH.replace("\\", "\\\\") + "\","
                + "\"appPackage\":\"" + PACKAGE_NAME + "\","
                + "\"appActivity\":\"" + ACTIVITY_NAME + "\","
                + "\"automationName\":\"UiAutomator2\","
                + "\"newCommandTimeout\":300,"
                + "\"autoGrantPermissions\":true,"
                + "\"noReset\":false,"
                + "\"fullReset\":false"
                + "}"
                + "}";

        sendMcpRequest("tools/call", "{\"name\":\"create_session\",\"arguments\":" + sessionCapabilities + "}");

        logger.info("⏳ Waiting for app to launch (20 seconds)...");
        Thread.sleep(20000); // Wait for app to fully launch
    }

    private static void runSingleScenario() throws IOException, InterruptedException {
        logger.info("");
        logger.info("🎬 SCENARIO: View Product Catalog");
        logger.info("==================================");

        // GIVEN: the SauceLabs app is launched
        logger.info("📋 GIVEN: the SauceLabs app is launched");
        logger.info("   📸 Taking screenshot to verify app is launched...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);

        // WHEN: I view the product catalog
        logger.info("📋 WHEN: I view the product catalog");
        logger.info("   🔍 Generating locators for current screen...");
        sendMcpRequest("tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(5000);

        // THEN: I should see Sauce Labs Backpack
        logger.info("📋 THEN: I should see Sauce Labs Backpack");
        logger.info("   🔎 Looking for 'Sauce Labs Backpack' element...");
        sendMcpRequest("tools/call",
                "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//android.widget.EditText[@content-desc='test-Username']\"}}");
        Thread.sleep(3000);

        // THEN: I should see Sauce Labs Backpack
        logger.info("📋 THEN: I should see Sauce Labs Backpack");
        logger.info("   🔎 Looking for 'Sauce Labs Backpack' element...");
        sendMcpRequest("tools/call",
                "{ \"name\": \"generate://code-with-locators\", \"arguments\": { \"steps\": [ " +
                        "\"Use appium_find_element to locate the login button\"" +
                        "]}}");

        Thread.sleep(3000);

        // AND: I should see Sauce Labs Bike Light
        logger.info("📋 AND: I should see Sauce Labs Bike Light");
        logger.info("   🔎 Looking for 'Sauce Labs Bike Light' element...");
        sendMcpRequest("tools/call",
                "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@text,'Sauce Labs Bike Light')]\"}}}");
        Thread.sleep(3000);

        // AND: I should see the sort button
        logger.info("📋 AND: I should see the sort button");
        logger.info("   🔎 Looking for sort button...");
        sendMcpRequest("tools/call",
                "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"//*[contains(@content-desc,'sort') or contains(@text,'sort') or contains(@text,'Sort')]\"}}}");
        Thread.sleep(3000);

        logger.info("");
        logger.info("✅ Single scenario completed!");
        logger.info("📸 Taking final screenshot...");
        sendMcpRequest("tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(3000);
    }

    private static void startOutputReaders() {
        Thread outputThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    logger.info("📥 MCP: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Output reader finished");
            }
        });

        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    logger.debug("🔧 MCP Debug: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Error reader finished");
            }
        });

        outputThread.setDaemon(true);
        errorThread.setDaemon(true);
        outputThread.start();
        errorThread.start();
    }

    private static void sendMcpRequest(String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method
                + "\",\"params\":" + params + "}";
        logger.debug("📤 Sending: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
}
