package com.jarvis.appium.cucumber.utils;

import com.jarvis.appium.cucumber.model.ElementLocator;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

/**
 * Manager class for handling element locators and operations
 */
public class ElementManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ElementManager.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    // HashMap to store elements by their content description or text
    private static final Map<String, ElementLocator> elementMap = new HashMap<>();
    
    /**
     * Parse generate_locators response and store elements in HashMap
     */
    public static void parseAndStoreElements(String generateLocatorsResponse) {
        try {
            logger.info("🔍 Parsing generate_locators response...");
            
            // Parse the JSON response
            JsonNode rootNode = objectMapper.readTree(generateLocatorsResponse);
            JsonNode resultNode = rootNode.path("result");
            JsonNode contentArray = resultNode.path("content");
            
            if (contentArray.isArray() && contentArray.size() > 0) {
                JsonNode firstContent = contentArray.get(0);
                String textContent = firstContent.path("text").asText();
                
                // Parse the inner JSON containing interactableElements
                JsonNode innerJson = objectMapper.readTree(textContent);
                JsonNode elementsArray = innerJson.path("interactableElements");
                
                if (elementsArray.isArray()) {
                    elementMap.clear(); // Clear previous elements
                    
                    for (JsonNode elementNode : elementsArray) {
                        ElementLocator element = parseElementFromJson(elementNode);
                        storeElement(element);
                    }
                    
                    logger.info("✅ Parsed and stored {} elements", elementMap.size());
                    logStoredElements();
                } else {
                    logger.warn("⚠️ No interactableElements array found in response");
                }
            } else {
                logger.warn("⚠️ No content found in generate_locators response");
            }
            
        } catch (Exception e) {
            logger.error("❌ Failed to parse generate_locators response: {}", e.getMessage());
        }
    }
    
    /**
     * Parse individual element from JSON node
     */
    private static ElementLocator parseElementFromJson(JsonNode elementNode) {
        ElementLocator element = new ElementLocator();
        
        element.setTagName(elementNode.path("tagName").asText());
        element.setText(elementNode.path("text").asText());
        element.setContentDesc(elementNode.path("contentDesc").asText());
        element.setResourceId(elementNode.path("resourceId").asText());
        element.setClickable(elementNode.path("clickable").asBoolean());
        element.setEnabled(elementNode.path("enabled").asBoolean());
        element.setDisplayed(elementNode.path("displayed").asBoolean());
        
        // Parse locators map
        JsonNode locatorsNode = elementNode.path("locators");
        Map<String, String> locators = new HashMap<>();
        locatorsNode.fields().forEachRemaining(entry -> {
            locators.put(entry.getKey(), entry.getValue().asText());
        });
        element.setLocators(locators);
        
        return element;
    }
    
    /**
     * Store element in HashMap using multiple keys for easy lookup
     */
    private static void storeElement(ElementLocator element) {
        // Store by content description if available
        if (element.getContentDesc() != null && !element.getContentDesc().isEmpty()) {
            elementMap.put(element.getContentDesc(), element);
            logger.debug("📝 Stored element by contentDesc: {}", element.getContentDesc());
        }
        
        // Store by text if available and different from contentDesc
        if (element.getText() != null && !element.getText().isEmpty() && 
            !element.getText().equals(element.getContentDesc())) {
            elementMap.put(element.getText(), element);
            logger.debug("📝 Stored element by text: {}", element.getText());
        }
        
        // Store by resource ID if available
        if (element.getResourceId() != null && !element.getResourceId().isEmpty()) {
            elementMap.put(element.getResourceId(), element);
            logger.debug("📝 Stored element by resourceId: {}", element.getResourceId());
        }
    }
    
    /**
     * Get element by key (contentDesc, text, or resourceId)
     */
    public static ElementLocator getElement(String key) {
        ElementLocator element = elementMap.get(key);
        if (element != null) {
            logger.debug("🎯 Found element for key '{}': {}", key, element.getTagName());
        } else {
            logger.warn("⚠️ No element found for key: {}", key);
        }
        return element;
    }
    
    /**
     * Find element using appium_find_element and store its UUID
     */
    public static void findAndStoreElementUuid(BufferedWriter writer, String key) throws Exception {
        ElementLocator element = getElement(key);
        if (element == null) {
            throw new RuntimeException("Element not found for key: " + key);
        }
        
        String strategy = element.getBestLocatorStrategy();
        String selector = element.getBestLocatorValue();
        
        logger.info("🔎 Finding element '{}' using {} = '{}'", key, strategy, selector);
        
        // Send appium_find_element request
        String findRequest = String.format(
            "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"%s\",\"selector\":\"%s\"}}",
            strategy, selector
        );
        
        McpUtils.sendMcpRequest(writer, "tools/call", findRequest);
        Thread.sleep(2000); // Wait for response
        
        // Note: In a real implementation, you would capture the response and extract the UUID
        // For now, we'll simulate storing a UUID
        String simulatedUuid = "element-" + System.currentTimeMillis();
        element.setUuid(simulatedUuid);
        
        logger.info("✅ Element '{}' found and UUID stored: {}", key, simulatedUuid);
    }
    
    /**
     * Click element using its stored UUID
     */
    public static void clickElement(BufferedWriter writer, String key) throws Exception {
        ElementLocator element = getElement(key);
        if (element == null) {
            throw new RuntimeException("Element not found for key: " + key);
        }

        if (!element.isClickableElement()) {
            throw new RuntimeException("Element is not clickable: " + key);
        }

        logger.info("🖱️ Clicking element '{}'", key);

        // First find the element to get its UUID
        String strategy = element.getBestLocatorStrategy();
        String selector = element.getBestLocatorValue();

        String findRequest = String.format(
            "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"%s\",\"selector\":\"%s\"}}",
            strategy, selector
        );

        McpUtils.sendMcpRequest(writer, "tools/call", findRequest);
        Thread.sleep(2000);

        // Note: In a real implementation, we would capture the UUID from the response
        // For now, we'll use a simulated approach and call click with the required parameters
        String clickRequest = String.format(
            "{\"name\":\"appium_click\",\"arguments\":{\"elementUUID\":\"temp-uuid\"}}",
            strategy, selector
        );

        McpUtils.sendMcpRequest(writer, "tools/call", clickRequest);
        Thread.sleep(2000);

        logger.info("✅ Clicked element: {}", key);
    }
    
    /**
     * Set value in input element
     */
    public static void setElementValue(BufferedWriter writer, String key, String value) throws Exception {
        ElementLocator element = getElement(key);
        if (element == null) {
            throw new RuntimeException("Element not found for key: " + key);
        }

        if (!element.isInputElement()) {
            throw new RuntimeException("Element is not an input field: " + key);
        }

        logger.info("⌨️ Setting value '{}' in element '{}'", value, key);

        // First find the element to get its UUID
        String strategy = element.getBestLocatorStrategy();
        String selector = element.getBestLocatorValue();

        String findRequest = String.format(
            "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"%s\",\"selector\":\"%s\"}}",
            strategy, selector
        );

        McpUtils.sendMcpRequest(writer, "tools/call", findRequest);
        Thread.sleep(2000);

        // Note: In a real implementation, we would capture the UUID from the response
        // For now, we'll use a simulated approach and call set_value with the required parameters
        String setValueRequest = String.format(
            "{\"name\":\"appium_set_value\",\"arguments\":{\"elementUUID\":\"temp-uuid\",\"text\":\"%s\"}}",
            value
        );

        McpUtils.sendMcpRequest(writer, "tools/call", setValueRequest);
        Thread.sleep(2000);

        logger.info("✅ Set value in element: {}", key);
    }
    
    /**
     * Get all stored elements
     */
    public static Map<String, ElementLocator> getAllElements() {
        return new HashMap<>(elementMap);
    }
    
    /**
     * Get elements by type (clickable, input, etc.)
     */
    public static List<ElementLocator> getClickableElements() {
        return elementMap.values().stream()
            .filter(ElementLocator::isClickableElement)
            .distinct()
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    public static List<ElementLocator> getInputElements() {
        return elementMap.values().stream()
            .filter(ElementLocator::isInputElement)
            .distinct()
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * Clear all stored elements
     */
    public static void clearElements() {
        elementMap.clear();
        logger.info("🧹 Cleared all stored elements");
    }
    
    /**
     * Log all stored elements for debugging
     */
    private static void logStoredElements() {
        logger.info("📋 Stored Elements Summary:");
        elementMap.forEach((key, element) -> {
            logger.info("  🔑 Key: '{}' -> {} ({})", 
                key, element.getTagName(), element.getBestLocatorStrategy());
        });
    }
}
