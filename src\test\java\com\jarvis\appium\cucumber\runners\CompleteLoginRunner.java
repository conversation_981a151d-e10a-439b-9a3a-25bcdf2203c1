package com.jarvis.appium.cucumber.runners;

import io.cucumber.junit.platform.engine.Constants;
import org.junit.platform.suite.api.ConfigurationParameter;
import org.junit.platform.suite.api.IncludeEngines;
import org.junit.platform.suite.api.SelectClasspathResource;
import org.junit.platform.suite.api.Suite;

/**
 * Complete Login Test Runner for Cucumber tests
 * Tests the complete login flow with UUID-based element interaction
 */
@Suite
@IncludeEngines("cucumber")
@SelectClasspathResource("features")
@ConfigurationParameter(key = Constants.FEATURES_PROPERTY_NAME, value = "src/test/resources/features/CompleteLogin.feature")
@ConfigurationParameter(key = Constants.GLUE_PROPERTY_NAME, value = "com.jarvis.appium.cucumber.steps")
@ConfigurationParameter(key = Constants.FILTER_TAGS_PROPERTY_NAME, value = "@CompleteLogin")
@ConfigurationParameter(key = Constants.PLUGIN_PROPERTY_NAME, value = "pretty,html:target/cucumber-reports/CompleteLogin")
public class CompleteLoginRunner {
    // This class serves as the entry point for running Cucumber tests
    // The actual test logic is in the step definition classes
}
