package com.jarvis.appium.cucumber.runners;

import io.cucumber.junit.platform.engine.Constants;
import org.junit.platform.suite.api.ConfigurationParameter;
import org.junit.platform.suite.api.IncludeEngines;
import org.junit.platform.suite.api.SelectClasspathResource;
import org.junit.platform.suite.api.Suite;

/**
 * Cucumber Runner for Single Scenario Tests
 *
 * Simple, single runner that executes Cucumber scenarios using JUnit Platform.
 * Based on the original SingleScenarioRunner functionality.
 *
 * Usage:
 *   mvn test -Dtest=SingleScenarioRunner
 *   mvn test -Dtest=SingleScenarioRunner -Dcucumber.filter.tags="@Smoke"
 */
@Suite
@IncludeEngines("cucumber")
@SelectClasspathResource("features")
@ConfigurationParameter(key = Constants.PLUGIN_PROPERTY_NAME, value = "pretty,html:target/cucumber-reports/html,json:target/cucumber-reports/json/Cucumber.json")
@ConfigurationParameter(key = Constants.GLUE_PROPERTY_NAME, value = "com.jarvis.appium.cucumber.steps")
@ConfigurationParameter(key = Constants.FEATURES_PROPERTY_NAME, value = "src/test/resources/features")
@ConfigurationParameter(key = Constants.FILTER_TAGS_PROPERTY_NAME, value = "@Login")
public class SingleScenarioRunner {
    // This class serves as the entry point for Cucumber tests
    // The actual test execution is handled by the Cucumber engine
}
