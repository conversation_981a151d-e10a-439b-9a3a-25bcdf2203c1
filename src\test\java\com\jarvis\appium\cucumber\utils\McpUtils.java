package com.jarvis.appium.cucumber.utils;

import com.jarvis.appium.cucumber.config.TestConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;

/**
 * Utility class for MCP operations in Cucumber tests
 * 
 * Provides common MCP functionality used across step definitions
 */
public class McpUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(McpUtils.class);
    private static int requestId = 1;
    
    /**
     * Send an MCP request
     */
    public static void sendMcpRequest(BufferedWriter writer, String method, String params) throws IOException {
        String request = "{\"jsonrpc\":\"2.0\",\"id\":\"" + (requestId++) + "\",\"method\":\"" + method
            + "\",\"params\":" + params + "}";
        logger.debug("📤 Sending MCP Request: {}", request);
        writer.write(request);
        writer.newLine();
        writer.flush();
    }
    
    /**
     * Take a screenshot using MCP
     */
    public static void takeScreenshot(BufferedWriter writer) throws IOException, InterruptedException {
        logger.info("📸 Taking screenshot...");
        sendMcpRequest(writer, "tools/call", "{\"name\":\"appium_screenshot\",\"arguments\":{}}");
        Thread.sleep(TestConfig.SCREENSHOT_TIMEOUT);
    }
    
    /**
     * Generate locators for current screen
     */
    public static void generateLocators(BufferedWriter writer) throws IOException, InterruptedException {
        logger.info("🔍 Generating locators for current screen...");
        sendMcpRequest(writer, "tools/call", "{\"name\":\"generate_locators\",\"arguments\":{}}");
        Thread.sleep(TestConfig.ELEMENT_WAIT_TIMEOUT);
    }
    
    /**
     * Find element using XPath
     */
    public static void findElementByXPath(BufferedWriter writer, String xpath) throws IOException, InterruptedException {
        logger.info("🔎 Finding element with XPath: {}", xpath);
        sendMcpRequest(writer, "tools/call",
            "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"" + xpath + "\"}}");
        Thread.sleep(TestConfig.ELEMENT_WAIT_TIMEOUT / 2);
    }
    
    /**
     * Find element by accessibility ID
     */
    public static void findElementByAccessibilityId(BufferedWriter writer, String accessibilityId) throws IOException, InterruptedException {
        logger.info("🔎 Finding element with accessibility ID: {}", accessibilityId);
        sendMcpRequest(writer, "tools/call",
            "{\"name\":\"appium_find_element\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"" + accessibilityId + "\"}}");
        Thread.sleep(TestConfig.ELEMENT_WAIT_TIMEOUT / 2);
    }
    
    /**
     * Click element using XPath
     */
    public static void clickElementByXPath(BufferedWriter writer, String xpath) throws IOException, InterruptedException {
        logger.info("🖱️ Clicking element with XPath: {}", xpath);
        sendMcpRequest(writer, "tools/call",
            "{\"name\":\"appium_click\",\"arguments\":{\"strategy\":\"xpath\",\"selector\":\"" + xpath + "\"}}");
        Thread.sleep(TestConfig.ELEMENT_WAIT_TIMEOUT / 2);
    }
    
    /**
     * Click element using accessibility ID
     */
    public static void clickElementByAccessibilityId(BufferedWriter writer, String accessibilityId) throws IOException, InterruptedException {
        logger.info("🖱️ Clicking element with accessibility ID: {}", accessibilityId);
        sendMcpRequest(writer, "tools/call",
            "{\"name\":\"appium_click\",\"arguments\":{\"strategy\":\"accessibility id\",\"selector\":\"" + accessibilityId + "\"}}");
        Thread.sleep(TestConfig.ELEMENT_WAIT_TIMEOUT / 2);
    }
    
    /**
     * Initialize MCP connection
     */
    public static void initializeMcp(BufferedWriter writer) throws IOException, InterruptedException {
        logger.info("🔗 Initializing MCP connection...");
        sendMcpRequest(writer, "initialize", TestConfig.getMcpInitParams());
        Thread.sleep(TestConfig.MCP_INIT_TIMEOUT);
    }
    
    /**
     * Select platform
     */
    public static void selectPlatform(BufferedWriter writer, String platform) throws IOException, InterruptedException {
        logger.info("📱 Selecting {} platform...", platform);
        sendMcpRequest(writer, "tools/call", "{\"name\":\"select_platform\",\"arguments\":{\"platform\":\"" + platform + "\"}}");
        Thread.sleep(TestConfig.MCP_INIT_TIMEOUT);
    }
    
    /**
     * Create session
     */
    public static void createSession(BufferedWriter writer) throws IOException, InterruptedException {
        logger.info("📲 Creating session with SauceLabs app...");
        sendMcpRequest(writer, "tools/call", "{\"name\":\"create_session\",\"arguments\":" + TestConfig.getSessionCapabilities() + "}");
        Thread.sleep(TestConfig.APP_LAUNCH_TIMEOUT);
    }
    
    /**
     * Start output readers for MCP process with base64 filtering
     */
    public static void startOutputReaders(BufferedReader reader, BufferedReader errorReader) {
        Thread outputThread = new Thread(() -> {
            try {
                String line;
                while ((line = reader.readLine()) != null) {
                    // Filter out base64 screenshot data to keep logs clean
                    if (isBase64ScreenshotData(line)) {
                        logger.info("📸 Screenshot captured (base64 data filtered for readability)");
                    } else {
                        logger.info("📥 MCP: {}", line);
                    }
                }
            } catch (IOException e) {
                logger.debug("Output reader finished");
            }
        });

        Thread errorThread = new Thread(() -> {
            try {
                String line;
                while ((line = errorReader.readLine()) != null) {
                    logger.debug("🔧 MCP Debug: {}", line);
                }
            } catch (IOException e) {
                logger.debug("Error reader finished");
            }
        });

        outputThread.setDaemon(true);
        errorThread.setDaemon(true);
        outputThread.start();
        errorThread.start();
    }

    /**
     * Check if the line contains base64 screenshot data
     */
    private static boolean isBase64ScreenshotData(String line) {
        return line.contains("\"screenshot\"") &&
               (line.contains("iVBORw0KGgo") || line.contains("data:image") || line.length() > 1000);
    }
}
